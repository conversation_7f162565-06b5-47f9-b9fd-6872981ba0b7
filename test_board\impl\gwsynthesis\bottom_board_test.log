GowinSynthesis start
Running parser ...
Analyzing Verilog file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\ddr3_memory_interface\ddr3_memory_interface.v'
Analyzing Verilog file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\ddr3_syn_top.v'
Undeclared symbol 'ddr_cs1', assumed default net type 'wire'("G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\ddr3_syn_top.v":143)
Analyzing Verilog file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\gowin_rpll\gowin_rpll.v'
Analyzing Verilog file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\key_blink.v'
Undeclared symbol 'rst_n', assumed default net type 'wire'("G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\key_blink.v":17)
Analyzing Verilog file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\tester.v'
Analyzing included file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\print.v'("G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\tester.v":559)
Back to file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\tester.v'("G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\tester.v":559)
Analyzing Verilog file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\top.v'
Analyzing Verilog file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\uart_tx_V2.v'
Compiling module 'top'("G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\top.v":1)
Compiling module 'key_blink(default_count=13500000,counter_1=5400000,counter_2=21600000,counter_3=32400000,counter_4=54000000,io_count=104)'("G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\key_blink.v":1)
Compiling module 'ddr3_syn_top'("G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\ddr3_syn_top.v":3)
Compiling module 'tester'("G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\tester.v":1)
Extracting RAM for identifier 'read_data'("G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\tester.v":127)
Extracting RAM for identifier 'print_seq'("G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\print.v":6)
Compiling module 'uart_tx_V2'("G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\uart_tx_V2.v":1)
WARN  (EX3791) : Expression size 11 truncated to fit in target size 10("G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\uart_tx_V2.v":49)
WARN  (EX1998) : Net 'print_length[6]' does not have a driver("G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\print.v":15)
Compiling module 'Gowin_rPLL'("G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\gowin_rpll\gowin_rpll.v":9)
Compiling module 'DDR3_Memory_Interface_Top'("G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\ddr3_memory_interface\ddr3_memory_interface.v":11484)
Compiling module '**'("G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\ddr3_memory_interface\ddr3_memory_interface.v":11483)
NOTE  (EX0101) : Current top module is "top"
[5%] Running netlist conversion ...
Running device independent optimization ...
[10%] Optimizing Phase 0 completed
[15%] Optimizing Phase 1 completed
[25%] Optimizing Phase 2 completed
Running inference ...
[30%] Inferring Phase 0 completed
[40%] Inferring Phase 1 completed
[50%] Inferring Phase 2 completed
[55%] Inferring Phase 3 completed
Running technical mapping ...
[60%] Tech-Mapping Phase 0 completed
[65%] Tech-Mapping Phase 1 completed
[75%] Tech-Mapping Phase 2 completed
[80%] Tech-Mapping Phase 3 completed
[90%] Tech-Mapping Phase 4 completed
[95%] Generate netlist file "G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\impl\gwsynthesis\bottom_board_test.vg" completed
[100%] Generate report file "G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\impl\gwsynthesis\bottom_board_test_syn.rpt.html" completed
GowinSynthesis finish
