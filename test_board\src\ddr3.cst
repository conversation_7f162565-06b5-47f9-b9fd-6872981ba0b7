//Copyright (C)2014-2022 Gowin Semiconductor Corporation.
//All rights reserved. 
//File Title: Physical Constraints file
//GOWIN Version: 1.9.8.06-1
//Part Number: GW2A-LV18PG256C8/I7
//Device: GW2A-18C
//Created Time: Mon 08 08 00:40:48 2022

IO_LOC "ddr_bank[2]" H5;
IO_PORT "ddr_bank[2]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_bank[1]" D3;
IO_PORT "ddr_bank[1]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_bank[0]" H4;
IO_PORT "ddr_bank[0]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_addr[13]" C8;
IO_PORT "ddr_addr[13]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_addr[12]" A3;
IO_PORT "ddr_addr[12]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_addr[11]" B7;
IO_PORT "ddr_addr[11]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_addr[10]" K3;
IO_PORT "ddr_addr[10]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_addr[9]" F9;
IO_PORT "ddr_addr[9]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_addr[8]" A5;
IO_PORT "ddr_addr[8]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_addr[7]" D8;
IO_PORT "ddr_addr[7]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_addr[6]" B1;
IO_PORT "ddr_addr[6]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_addr[5]" E6;
IO_PORT "ddr_addr[5]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_addr[4]" C4;
IO_PORT "ddr_addr[4]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_addr[3]" F8;
IO_PORT "ddr_addr[3]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_addr[2]" D6;
IO_PORT "ddr_addr[2]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_addr[1]" A4;
IO_PORT "ddr_addr[1]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_addr[0]" F7;
IO_PORT "ddr_addr[0]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_odt" R3;
IO_PORT "ddr_odt" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_cke" J2;
IO_PORT "ddr_cke" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_we" L2;
IO_PORT "ddr_we" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_cas" R6;
IO_PORT "ddr_cas" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_ras" R4;
IO_PORT "ddr_ras" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_reset_n" B9;
IO_PORT "ddr_reset_n" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_ck" J1,J3;
IO_PORT "ddr_ck" IO_TYPE=SSTL15D PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_dm[1]" K5;
IO_PORT "ddr_dm[1]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_dm[0]" G1;
IO_PORT "ddr_dm[0]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_dq[15]" M2;
IO_PORT "ddr_dq[15]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 VREF=INTERNAL BANK_VCCIO=1.5;
IO_LOC "ddr_dq[14]" R1;
IO_PORT "ddr_dq[14]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 VREF=INTERNAL BANK_VCCIO=1.5;
IO_LOC "ddr_dq[13]" H3;
IO_PORT "ddr_dq[13]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 VREF=INTERNAL BANK_VCCIO=1.5;
IO_LOC "ddr_dq[12]" P4;
IO_PORT "ddr_dq[12]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 VREF=INTERNAL BANK_VCCIO=1.5;
IO_LOC "ddr_dq[11]" L1;
IO_PORT "ddr_dq[11]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 VREF=INTERNAL BANK_VCCIO=1.5;
IO_LOC "ddr_dq[10]" N2;
IO_PORT "ddr_dq[10]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 VREF=INTERNAL BANK_VCCIO=1.5;
IO_LOC "ddr_dq[9]" K4;
IO_PORT "ddr_dq[9]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 VREF=INTERNAL BANK_VCCIO=1.5;
IO_LOC "ddr_dq[8]" M3;
IO_PORT "ddr_dq[8]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 VREF=INTERNAL BANK_VCCIO=1.5;
IO_LOC "ddr_dq[7]" B3;
IO_PORT "ddr_dq[7]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 VREF=INTERNAL BANK_VCCIO=1.5;
IO_LOC "ddr_dq[6]" E1;
IO_PORT "ddr_dq[6]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 VREF=INTERNAL BANK_VCCIO=1.5;
IO_LOC "ddr_dq[5]" C1;
IO_PORT "ddr_dq[5]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 VREF=INTERNAL BANK_VCCIO=1.5;
IO_LOC "ddr_dq[4]" E2;
IO_PORT "ddr_dq[4]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 VREF=INTERNAL BANK_VCCIO=1.5;
IO_LOC "ddr_dq[3]" F3;
IO_PORT "ddr_dq[3]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 VREF=INTERNAL BANK_VCCIO=1.5;
IO_LOC "ddr_dq[2]" F4;
IO_PORT "ddr_dq[2]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 VREF=INTERNAL BANK_VCCIO=1.5;
IO_LOC "ddr_dq[1]" F5;
IO_PORT "ddr_dq[1]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 VREF=INTERNAL BANK_VCCIO=1.5;
IO_LOC "ddr_dq[0]" G5;
IO_PORT "ddr_dq[0]" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 VREF=INTERNAL BANK_VCCIO=1.5;
IO_LOC "ddr_dqs[1]" J5,K6;
IO_PORT "ddr_dqs[1]" IO_TYPE=SSTL15D PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "ddr_dqs[0]" G2,G3;
IO_PORT "ddr_dqs[0]" IO_TYPE=SSTL15D PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "uart_txp" M11;
IO_PORT "uart_txp" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "ddr_cs" P5;
IO_PORT "ddr_cs" IO_TYPE=SSTL15 PULL_MODE=NONE DRIVE=8 BANK_VCCIO=1.5;
IO_LOC "onboard_pin[4]" N10;
IO_PORT "onboard_pin[4]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "onboard_pin[3]" R14;
IO_PORT "onboard_pin[3]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "onboard_pin[2]" N11;
IO_PORT "onboard_pin[2]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "onboard_pin[1]" P12;
IO_PORT "onboard_pin[1]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "onboard_pin[0]" M10;
IO_PORT "onboard_pin[0]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[103]" T11;
IO_PORT "led_o[103]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[102]" T14;
IO_PORT "led_o[102]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[101]" C12;
IO_PORT "led_o[101]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[100]" C11;
IO_PORT "led_o[100]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[99]" A14;
IO_PORT "led_o[99]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[98]" T13;
IO_PORT "led_o[98]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[97]" A12;
IO_PORT "led_o[97]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[96]" P13;
IO_PORT "led_o[96]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[95]" B14;
IO_PORT "led_o[95]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[94]" B13;
IO_PORT "led_o[94]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[93]" B12;
IO_PORT "led_o[93]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[92]" A11;
IO_PORT "led_o[92]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[91]" A15;
IO_PORT "led_o[91]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[90]" P11;
IO_PORT "led_o[90]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[89]" R11;
IO_PORT "led_o[89]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[88]" R13;
IO_PORT "led_o[88]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[87]" T15;
IO_PORT "led_o[87]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[86]" R12;
IO_PORT "led_o[86]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[85]" C9;
IO_PORT "led_o[85]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[84]" A9;
IO_PORT "led_o[84]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[83]" E10;
IO_PORT "led_o[83]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[82]" D11;
IO_PORT "led_o[82]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[81]" D10;
IO_PORT "led_o[81]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[80]" F10;
IO_PORT "led_o[80]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[79]" R16;
IO_PORT "led_o[79]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[78]" P16;
IO_PORT "led_o[78]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[77]" N16;
IO_PORT "led_o[77]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[76]" L16;
IO_PORT "led_o[76]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[75]" P15;
IO_PORT "led_o[75]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[74]" N15;
IO_PORT "led_o[74]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[73]" M15;
IO_PORT "led_o[73]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[72]" K15;
IO_PORT "led_o[72]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[71]" N14;
IO_PORT "led_o[71]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[70]" M14;
IO_PORT "led_o[70]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[69]" L14;
IO_PORT "led_o[69]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[68]" K14;
IO_PORT "led_o[68]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[67]" L13;
IO_PORT "led_o[67]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[66]" K13;
IO_PORT "led_o[66]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[65]" L12;
IO_PORT "led_o[65]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[64]" K12;
IO_PORT "led_o[64]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[63]" K11;
IO_PORT "led_o[63]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[62]" J11;
IO_PORT "led_o[62]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[61]" E15;
IO_PORT "led_o[61]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[60]" D14;
IO_PORT "led_o[60]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[59]" B11;
IO_PORT "led_o[59]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[58]" T9;
IO_PORT "led_o[58]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[57]" T8;
IO_PORT "led_o[57]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[56]" T7;
IO_PORT "led_o[56]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[55]" T6;
IO_PORT "led_o[55]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[54]" R10;
IO_PORT "led_o[54]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[53]" R9;
IO_PORT "led_o[53]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[52]" R8;
IO_PORT "led_o[52]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[51]" R7;
IO_PORT "led_o[51]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[50]" P10;
IO_PORT "led_o[50]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[49]" P9;
IO_PORT "led_o[49]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[48]" P8;
IO_PORT "led_o[48]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[47]" P7;
IO_PORT "led_o[47]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[46]" P6;
IO_PORT "led_o[46]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[45]" N9;
IO_PORT "led_o[45]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[44]" N8;
IO_PORT "led_o[44]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[43]" N7;
IO_PORT "led_o[43]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[42]" N6;
IO_PORT "led_o[42]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[41]" M9;
IO_PORT "led_o[41]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[40]" M8;
IO_PORT "led_o[40]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[39]" M7;
IO_PORT "led_o[39]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[38]" M6;
IO_PORT "led_o[38]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[37]" L10;
IO_PORT "led_o[37]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[36]" L9;
IO_PORT "led_o[36]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[35]" L8;
IO_PORT "led_o[35]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[34]" C13;
IO_PORT "led_o[34]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[33]" A13;
IO_PORT "led_o[33]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[32]" C10;
IO_PORT "led_o[32]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_PORT "led_o[31]" IO_TYPE=LVCMOS33;
IO_LOC "led_o[30]" B16;
IO_PORT "led_o[30]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[29]" C15;
IO_PORT "led_o[29]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[28]" M16;
IO_PORT "led_o[28]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[27]" K16;
IO_PORT "led_o[27]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[26]" J16;
IO_PORT "led_o[26]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[25]" H16;
IO_PORT "led_o[25]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[24]" G16;
IO_PORT "led_o[24]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[23]" F16;
IO_PORT "led_o[23]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[22]" E16;
IO_PORT "led_o[22]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[21]" D16;
IO_PORT "led_o[21]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[20]" C16;
IO_PORT "led_o[20]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[19]" L15;
IO_PORT "led_o[19]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[18]" J15;
IO_PORT "led_o[18]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[17]" H15;
IO_PORT "led_o[17]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[16]" G15;
IO_PORT "led_o[16]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[15]" F15;
IO_PORT "led_o[15]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[14]" D15;
IO_PORT "led_o[14]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[13]" J14;
IO_PORT "led_o[13]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[12]" H14;
IO_PORT "led_o[12]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[11]" G14;
IO_PORT "led_o[11]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[10]" F14;
IO_PORT "led_o[10]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[9]" E14;
IO_PORT "led_o[9]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[8]" J13;
IO_PORT "led_o[8]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[7]" H13;
IO_PORT "led_o[7]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[6]" G13;
IO_PORT "led_o[6]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[5]" F13;
IO_PORT "led_o[5]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[4]" J12;
IO_PORT "led_o[4]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[3]" H12;
IO_PORT "led_o[3]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[2]" G12;
IO_PORT "led_o[2]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[1]" F12;
IO_PORT "led_o[1]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led_o[0]" G11;
IO_PORT "led_o[0]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "user_key[4]" E9;
IO_PORT "user_key[4]" IO_TYPE=LVCMOS15 PULL_MODE=NONE;
IO_LOC "user_key[3]" E8;
IO_PORT "user_key[3]" IO_TYPE=LVCMOS15 PULL_MODE=NONE;
IO_LOC "user_key[2]" T4;
IO_PORT "user_key[2]" IO_TYPE=LVCMOS15 PULL_MODE=NONE;
IO_LOC "user_key[1]" T3;
IO_PORT "user_key[1]" IO_TYPE=LVCMOS15 PULL_MODE=NONE;
IO_LOC "rst_n[1]" T2;
IO_PORT "rst_n[1]" PULL_MODE=NONE;
IO_LOC "rst_n[0]" T10;
IO_PORT "rst_n[0]" PULL_MODE=NONE;
IO_LOC "clk" H11;
IO_PORT "clk" IO_TYPE=LVCMOS33 PULL_MODE=NONE;
