<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>Timing Report Navigation</title>
<style type="text/css">
@import url(../temp/style.css);
body { font-family: Verdana, Arial, sans-serif; font-size: 12px; }
div#catalog_wrapper { width: 100%; }
div#catalog ul { list-style: none; margin-left: -15px; }
div#catalog ul li { margin: 3px 0 3px 0; text-align: left; color: #0084ff; white-space: nowrap; word-break: keep-all;  }
div#catalog a { display:inline-block; text-decoration: none; color: #0084ff; font-weight: bold; padding: 4px; margin: 0 0 0 0; }
div#catalog a:visited { color: #0084ff; }
div#catalog a:hover { color: #fff; background: #0084ff; }
div.triangle_fake, div.triangle { display: inline-block; cursor: pointer; width: 8px; height: 0; border-top: 5px solid transparent; border-bottom: 5px solid transparent; }
div.triangle_fake { border-left: 5px solid transparent; }
div.triangle { border-left: 5px solid #0084ff; }
div.triangle:hover { border-left-color: #000; }
</style>
<script>
function onClick(obj){var childs=obj.parentNode.childNodes;for(var i=0;i<childs.length;i++){if(childs[i].tagName=="UL"){if(childs[i].style.display=="none"){childs[i].style.display="block"}else{childs[i].style.display="none"}}}};
</script>
</head>
<body>
<div id="catalog_wrapper">
<div id="catalog">
<ul>
<!-- messages begin-->
<li><div class="triangle_fake"></div><a href="bottom_board_test_tr_content.html#Message" target="mainFrame" style=" font-size: 16px;">Timing Messages</a></li>
<!-- messages end-->
<!-- summaries begin-->
<li><div class="triangle" onclick="onClick(this)"></div><a href="bottom_board_test_tr_content.html#Summary" style=" font-size: 16px;" target="mainFrame">Timing Summaries</a>
<ul>
<li><div class="triangle_fake"></div><a href="bottom_board_test_tr_content.html#STA_Tool_Run_Summary" style=" font-size: 14px;" target="mainFrame">STA Tool Run Summary</a></li>
<li><div class="triangle_fake"></div><a href="bottom_board_test_tr_content.html#Clock_Report" style=" font-size: 14px;" target="mainFrame">Clock Summary</a></li>
<li><div class="triangle_fake"></div><a href="bottom_board_test_tr_content.html#Max_Frequency_Report" style=" font-size: 14px;color: #FF0000;" class = "error" target="mainFrame">Max Frequency Summary</a></li>
<li><div class="triangle_fake"></div><a href="bottom_board_test_tr_content.html#Total_Negative_Slack_Report" style=" font-size: 14px;" target="mainFrame">Total Negative Slack Summary</a></li>
</ul>
</li>
<!-- summaries end-->
<!-- details begin-->
<li><div class="triangle" onclick="onClick(this)"></div><a href="bottom_board_test_tr_content.html#Detail" style=" font-size: 16px;" target="mainFrame">Timing Details</a>
<ul>
<!--All_Path_Slack_Table begin-->
<li><div class="triangle" onclick="onClick(this)"></div><a href="bottom_board_test_tr_content.html#All_Path_Slack_Table" style=" font-size: 14px;" target="mainFrame">Path Slacks Table</a>
<ul>
<!--Setup_Slack_Table begin-->
<li><div class="triangle_fake"></div><a href="bottom_board_test_tr_content.html#Setup_Slack_Table" style=" font-size: 13px;color: #FF0000;" class = "error" target="mainFrame">Setup Paths Table</a>
</li>
<!--Setup_Slack_Table end-->
<!--Hold_Slack_Table begin-->
<li><div class="triangle_fake"></div><a href="bottom_board_test_tr_content.html#Hold_Slack_Table" style=" font-size: 13px;" target="mainFrame">Hold Paths Table</a>
</li>
<!--Hold_Slack_Table end-->
<!--Recovery_Slack_Table begin-->
<li><div class="triangle_fake"></div><a href="bottom_board_test_tr_content.html#Recovery_Slack_Table" style=" font-size: 13px;" target="mainFrame">Recovery Paths Table</a>
</li>
<!--Recovery_Slack_Table end-->
<!--Removal_Slack_Table begin-->
<li><div class="triangle_fake"></div><a href="bottom_board_test_tr_content.html#Removal_Slack_Table" style=" font-size: 13px;" target="mainFrame">Removal Paths Table</a>
</li>
<!--Removal_Slack_Table end-->
</ul>
</li><!--All_Path_Slack_Table end-->
<!--MIN_PULSE_WIDTH_TABLE begin-->
<li><div class="triangle_fake"></div><a href="bottom_board_test_tr_content.html#MIN_PULSE_WIDTH_TABLE" style=" font-size: 14px;" target="mainFrame">Minimum Pulse Width Table</a>
</li>
<!--MIN_PULSE_WIDTH_TABLE end-->
<!--Timing_Report_by_Analysis_Type begin-->
<li><div class="triangle" onclick="onClick(this)"></div><a href="bottom_board_test_tr_content.html#Timing_Report_by_Analysis_Type" style=" font-size: 14px;" target="mainFrame">Timing Report By Analysis Type</a>
<ul>
<!--Setup_Analysis begin-->
<li><div class="triangle_fake"></div><a href="bottom_board_test_tr_content.html#Setup_Analysis" style=" font-size: 13px;" target="mainFrame">Setup Analysis Report</a>
</li>
<!--Setup_Analysis end-->
<!--Hold_Analysis begin-->
<li><div class="triangle_fake"></div><a href="bottom_board_test_tr_content.html#Hold_Analysis" style=" font-size: 13px;" target="mainFrame">Hold Analysis Report</a>
</li>
<!--Hold_Analysis end-->
<!--Recovery_Analysis begin-->
<li><div class="triangle_fake"></div><a href="bottom_board_test_tr_content.html#Recovery_Analysis" style=" font-size: 13px;" target="mainFrame">Recovery Analysis Report</a>
</li>
<!--Recovery_Analysis end-->
<!--Removal_Analysis begin-->
<li><div class="triangle_fake"></div><a href="bottom_board_test_tr_content.html#Removal_Analysis" style=" font-size: 13px;" target="mainFrame">Removal Analysis Report</a>
</li>
<!--Removal_Analysis end-->
</ul>
</li>
<!--Timing_Report_by_Analysis_Type end-->
<!--Minimum_Pulse_Width_Report begin-->
<li><div class="triangle_fake"></div><a href="bottom_board_test_tr_content.html#Minimum_Pulse_Width_Report" style=" font-size: 14px;" target="mainFrame">Minimum Pulse Width Report</a>
</li>
<!--Minimum_Pulse_Width_Report end-->
<!--High_Fanout_Nets_Report begin-->
<li><div class="triangle_fake"></div><a href="bottom_board_test_tr_content.html#High_Fanout_Nets_Report" style=" font-size: 14px;" target="mainFrame">High Fanout Nets Report</a></li>
<!--High_Fanout_Nets_Report end-->
<!--Route_Congestions_Report begin-->
<li><div class="triangle_fake"></div><a href="bottom_board_test_tr_content.html#Route_Congestions_Report" style=" font-size: 14px;" target="mainFrame">Route Congestions Report</a></li>
<!--Route_Congestions_Report end-->
<!--Timing_Exceptions_Report begin-->
<li><div class="triangle" onclick="onClick(this)"></div><a href="bottom_board_test_tr_content.html#Timing_Exceptions_Report" style=" font-size: 14px;" target="mainFrame">Timing Exceptions Report</a>
<ul>
<!--Setup_Analysis_Exceptions begin-->
<li><div class="triangle_fake"></div><a href="bottom_board_test_tr_content.html#Setup_Analysis_Exceptions" style=" font-size: 13px;" target="mainFrame">Setup Analysis Report</a>
</li>
<!--Setup_Analysis_Exceptions end-->
<!--Hold_Analysis_Exceptions begin-->
<li><div class="triangle_fake"></div><a href="bottom_board_test_tr_content.html#Hold_Analysis_Exceptions" style=" font-size: 13px;" target="mainFrame">Hold Analysis Report</a>
</li>
<!--Hold_Analysis_Exceptions end-->
<!--Recovery_Analysis_Exceptions begin-->
<li><div class="triangle_fake"></div><a href="bottom_board_test_tr_content.html#Recovery_Analysis_Exceptions" style=" font-size: 13px;" target="mainFrame">Recovery Analysis Report</a>
</li>
<!--Recovery_Analysis_Exceptions end-->
<!--Removal_Analysis_Exceptions begin-->
<li><div class="triangle_fake" onclick="onClick(this)"></div><a href="bottom_board_test_tr_content.html#Removal_Analysis_Exceptions" style=" font-size: 13px;" target="mainFrame">Removal Analysis Report</a>
</li>
<!--Removal_Analysis_Exceptions end-->
</ul>
</li>
<!--Timing_Exceptions_Report end-->
<!--SDC_Report begin-->
<li><div class="triangle_fake"></div><a href="bottom_board_test_tr_content.html#SDC_Report" style=" font-size: 14px;" target="mainFrame">Timing Constraints Report</a></li>
<!--SDC_Report end-->
</ul>
</li>
<!-- details end-->
</ul>
</div><!-- catalog -->
</div><!-- catalog_wrapper -->
</body>
</html>
