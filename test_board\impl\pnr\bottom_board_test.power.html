<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//ENhttp://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>Power Analysis Report</title>
<style type="text/css">
body { font-family: Verdana, Arial, sans-serif; font-size: 12px; }
div#main_wrapper { width: 100%; }
div#content { margin-left: 350px; margin-right: 30px; }
div#catalog_wrapper {position: fixed; top: 30px; width: 350px; float: left; }
div#catalog ul { list-style-type: none; }
div#catalog li { text-align: left; list-style-type:circle; color: #0084ff; margin-top: 3px; margin-bottom: 3px; }
div#catalog a { display:inline-block; text-decoration: none; color: #0084ff; font-weight: bold; padding: 3px; }
div#catalog a:visited { color: #0084ff; }
div#catalog a:hover { color: #fff; background: #0084ff; }
hr { margin-top: 30px; margin-bottom: 30px; }
h1, h3 { text-align: center; }
h1 {margin-top: 50px; }
table, th, td {white-space:pre;  border: 1px solid #aaa; }
table { border-collapse:collapse; margin-top: 10px; margin-bottom: 20px; width: 100%; }
th, td { padding: 5px 5px 5px 5px; }
th { color: #fff; font-weight: bold; background-color: #0084ff; }
table.summary_table td.label { width: 24%; min-width: 200px; background-color: #dee8f4; }
table.thermal_table td.label { width: 24%; min-width: 200px; background-color: #dee8f4; }
table.Configure_table td.label { width: 24%; min-width: 200px; background-color: #dee8f4; }
table.detail_table th.label {  min-width: 8%; width: 8%; }
</style>
</head>
<body>
<div id="main_wrapper">
<div id="catalog_wrapper">
<div id="catalog">
<ul>
<li><a href="#Message" style=" font-size: 16px;">Power Messages</a>
<ul>
<li><a href="#Configure_Info" style=" font-size: 14px;">Configure Information</a></li>
</ul>
</li>
<li><a href="#Summary" style=" font-size: 16px;">Power Summary</a>
<ul>
<li><a href="#Power_Info" style=" font-size: 14px;">Power Information</a></li>
<li><a href="#Thermal_Info" style=" font-size: 14px;">Thermal Information</a></li>
<li><a href="#Supply_Summary" style=" font-size: 14px;">Supply Information</a></li>
</ul>
</li>
<li><a href="#Detail" style=" font-size: 16px;">Power Details</a>
<ul>
<li><a href="#By_Block_Type" style=" font-size: 14px;">Power By Block Type</a></li>
<li><a href="#By_Hierarchy" style=" font-size: 14px;">Power By Hierarchy</a></li>
<li><a href="#By_Clock_Domain" style=" font-size: 14px;">Power By Clock Domain</a></li>
</ul>
</li>
</ul>
</div><!-- catalog -->
</div><!-- catalog_wrapper -->
<div id="content">
<h1><a name="Message">Power Messages</a></h1>
<table class="summary_table">
<tr>
<td class="label">Report Title</td>
<td>Power Analysis Report</td>
</tr>
<tr>
<td class="label">Design File</td>
<td>G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\impl\gwsynthesis\bottom_board_test.vg</td>
</tr>
<tr>
<td class="label">Physical Constraints File</td>
<td>G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\ddr3.cst</td>
</tr>
<tr>
<td class="label">Timing Constraints File</td>
<td>G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\ddr3.sdc</td>
</tr>
<tr>
<td class="label">Tool Version</td>
<td>V1.9.11.03 Education</td>
</tr>
<tr>
<td class="label">Part Number</td>
<td>GW2A-LV18PG256C8/I7</td>
</tr>
<tr>
<td class="label">Device</td>
<td>GW2A-18</td>
</tr>
<tr>
<td class="label">Device Version</td>
<td>C</td>
</tr>
<tr>
<td class="label">Created Time</td>
<td>Wed Jul 30 11:45:43 2025
</td>
</tr>
<tr>
<td class="label">Legal Announcement</td>
<td>Copyright (C)2014-2025 Gowin Semiconductor Corporation. All rights reserved.</td>
</tr>
</table>
<h2><a name="Configure_Info">Configure Information:</a></h2>
<table class="summary_table">
<tr>
<td class="label">Grade</td>
<td>Commercial</td>
</tr>
<tr>
<td class="label">Process</td>
<td>Typical</td>
</tr>
<tr>
<td class="label">Ambient Temperature</td>
<td>25.000
</tr>
<tr>
<td class="label">Use Custom Theta JA</td>
<td>false</td>
</tr>
<tr>
<td class="label">Heat Sink</td>
<td>None</td>
</tr>
<tr>
<td class="label">Air Flow</td>
<td>LFM_0</td>
</tr>
<tr>
<td class="label">Use Custom Theta SA</td>
<td>false</td>
</tr>
<tr>
<td class="label">Board Thermal Model</td>
<td>None</td>
</tr>
<tr>
<td class="label">Use Custom Theta JB</td>
<td>false</td>
</tr>
<tr>
<td class="label">Related Vcd File</td>
<td></td>
</tr>
<tr>
<td class="label">Related Saif File</td>
<td></td>
</tr>
<tr>
<td class="label">Filter Glitches</td>
<td>false</td>
</tr>
<tr>
<td class="label">Default IO Toggle Rate</td>
<td>0.125</td>
</tr>
<tr>
<td class="label">Default Remain Toggle Rate</td>
<td>0.125</td>
</tr>
</table>
<h1><a name="Summary">Power Summary</a></h1>
<h2><a name="Power_Info">Power Information:</a></h2>
<table class="summary_table">
<tr>
<td class="label">Total Power (mW)</td>
<td>547.155</td>
</tr>
<tr>
<td class="label">Quiescent Power (mW)</td>
<td>122.157</td>
</tr>
<tr>
<td class="label">Dynamic Power (mW)</td>
<td>424.997</td>
</tr>
</table>
<h2><a name="Thermal_Info">Thermal Information:</a></h2>
<table class="summary_table">
<tr>
<td class="label">Junction Temperature</td>
<td>42.520</td>
</tr>
<tr>
<td class="label">Theta JA</td>
<td>32.020</td>
</tr>
<tr>
<td class="label">Max Allowed Ambient Temperature</td>
<td>67.480</td>
</tr>
</table>
<h2><a name="Supply_Summary">Supply Information:</a></h2>
<table class="summary_table">
<tr>
<th class="label">Voltage Source</th>
<th class="label">Voltage</th>
<th class="label">Dynamic Current(mA)</th>
<th class="label">Quiescent Current(mA)</th>
<th class="label">Power(mW)</th>
</tr>
<tr>
<td>VCC</td>
<td>1.000</td>
<td>351.653</td>
<td>69.939</td>
<td>421.592</td>
</tr>
<tr>
<td>VCCX</td>
<td>3.300</td>
<td>14.052</td>
<td>15.000</td>
<td>95.872</td>
</tr>
<tr>
<td>VCCIO15</td>
<td>1.500</td>
<td>10.848</td>
<td>0.291</td>
<td>16.708</td>
</tr>
<tr>
<td>VCCIO33</td>
<td>3.300</td>
<td>3.242</td>
<td>0.691</td>
<td>12.982</td>
</tr>
</table>
<h1><a name="Detail">Power Details</a></h1>
<h2><a name="By_Block_Type">Power By Block Type:</a></h2>
<table class="detail_table">
<tr>
<th class="label">Block Type</th>
<th class="label">Total Power(mW)</th>
<th class="label">Static Power(mW)</th>
<th class="label">Average Toggle Rate(millions of transitions/sec)</th>
</tr>
<tr>
<td>Logic</td>
<td>4.519</td>
<td>NA</td>
<td>10.302</td>
</tr>
<tr>
<td>IO</td>
<td>122.042
<td>34.083
<td>23.228
</tr>
<tr>
<td>BSRAM</td>
<td>198.612
<td>NA</td>
<td>NA</td>
</tr>
<tr>
<td>PLL</td>
<td>41.658
<td>NA</td>
<td>NA</td>
</tr>
<tr>
<td>DLL</td>
<td>92.160
<td>NA</td>
<td>NA</td>
</tr>
<tr>
<td>DQS</td>
<td>231.300
<td>NA</td>
<td>NA</td>
</tr>
</table>
<h2><a name="By_Hierarchy">Power By Hierarchy:</a></h2>
<table class="detail_table">
<tr>
<th class="label">Hierarchy Entity</th>
<th class="label">Total Power(mW)</th>
<th class="label">Block Dynamic Power(mW)</th>
</tr>
<tr>
<td>top</td>
<td>568.249</td>
<td>568.249(568.249)</td>
<tr>
<td>top/ddr3_syn_top_inst/</td>
<td>568.166</td>
<td>568.166(568.166)</td>
<tr>
<td>top/ddr3_syn_top_inst/pll/</td>
<td>41.658</td>
<td>41.658(0.000)</td>
<tr>
<td>top/ddr3_syn_top_inst/test/</td>
<td>69.111</td>
<td>69.111(0.026)</td>
<tr>
<td>top/ddr3_syn_top_inst/test/tx/</td>
<td>0.026</td>
<td>0.026(0.000)</td>
<tr>
<td>top/ddr3_syn_top_inst/u_ddr3/</td>
<td>457.397</td>
<td>457.397(457.397)</td>
<tr>
<td>top/ddr3_syn_top_inst/u_ddr3/gw3_top/</td>
<td>457.397</td>
<td>457.397(457.397)</td>
<tr>
<td>top/ddr3_syn_top_inst/u_ddr3/gw3_top/i4/</td>
<td>455.816</td>
<td>455.816(363.291)</td>
<tr>
<td>top/ddr3_syn_top_inst/u_ddr3/gw3_top/i4/ddr3_sync/</td>
<td>0.024</td>
<td>0.024(0.000)</td>
<tr>
<td>top/ddr3_syn_top_inst/u_ddr3/gw3_top/i4/u_ddr_phy_init/</td>
<td>0.583</td>
<td>0.583(0.000)</td>
<tr>
<td>top/ddr3_syn_top_inst/u_ddr3/gw3_top/i4/u_ddr_phy_wd/</td>
<td>362.684</td>
<td>362.684(362.677)</td>
<tr>
<td>top/ddr3_syn_top_inst/u_ddr3/gw3_top/i4/u_ddr_phy_wd/data_lane_gen[0].u_ddr3_phy_data_lane/</td>
<td>246.704</td>
<td>246.704(246.696)</td>
<tr>
<td>top/ddr3_syn_top_inst/u_ddr3/gw3_top/i4/u_ddr_phy_wd/data_lane_gen[0].u_ddr3_phy_data_lane/u_ddr3_phy_data_io/</td>
<td>115.655</td>
<td>115.655(0.000)</td>
<tr>
<td>top/ddr3_syn_top_inst/u_ddr3/gw3_top/i4/u_ddr_phy_wd/data_lane_gen[0].u_ddr3_phy_data_lane/u_in_fifo/</td>
<td>65.520</td>
<td>65.520(0.000)</td>
<tr>
<td>top/ddr3_syn_top_inst/u_ddr3/gw3_top/i4/u_ddr_phy_wd/data_lane_gen[0].u_ddr3_phy_data_lane/u_out_fifo/</td>
<td>65.521</td>
<td>65.521(0.000)</td>
<tr>
<td>top/ddr3_syn_top_inst/u_ddr3/gw3_top/i4/u_ddr_phy_wd/data_lane_gen[1].u_ddr3_phy_data_lane/</td>
<td>115.659</td>
<td>115.659(115.650)</td>
<tr>
<td>top/ddr3_syn_top_inst/u_ddr3/gw3_top/i4/u_ddr_phy_wd/data_lane_gen[1].u_ddr3_phy_data_lane/u_ddr3_phy_data_io/</td>
<td>115.650</td>
<td>115.650(0.000)</td>
<tr>
<td>top/ddr3_syn_top_inst/u_ddr3/gw3_top/i4/u_ddr_phy_wd/u_ddr3_phy_cmd_lane/</td>
<td>0.211</td>
<td>0.211(0.148)</td>
<tr>
<td>top/ddr3_syn_top_inst/u_ddr3/gw3_top/i4/u_ddr_phy_wd/u_ddr3_phy_cmd_lane/u_cmd_fifo/</td>
<td>0.148</td>
<td>0.148(0.000)</td>
<tr>
<td>top/ddr3_syn_top_inst/u_ddr3/gw3_top/i4/u_ddr_phy_wd/u_fifo_ctrl/</td>
<td>0.103</td>
<td>0.103(0.000)</td>
<tr>
<td>top/ddr3_syn_top_inst/u_ddr3/gw3_top/u_gwmc_top/</td>
<td>1.580</td>
<td>1.580(1.320)</td>
<tr>
<td>top/ddr3_syn_top_inst/u_ddr3/gw3_top/u_gwmc_top/gw_cmd0/</td>
<td>0.320</td>
<td>0.320(0.000)</td>
<tr>
<td>top/ddr3_syn_top_inst/u_ddr3/gw3_top/u_gwmc_top/gw_rd_data0/</td>
<td>0.195</td>
<td>0.195(0.000)</td>
<tr>
<td>top/ddr3_syn_top_inst/u_ddr3/gw3_top/u_gwmc_top/gw_wr_data0/</td>
<td>0.307</td>
<td>0.307(0.305)</td>
<tr>
<td>top/ddr3_syn_top_inst/u_ddr3/gw3_top/u_gwmc_top/gw_wr_data0/wr_fifo/</td>
<td>0.305</td>
<td>0.305(0.000)</td>
<tr>
<td>top/ddr3_syn_top_inst/u_ddr3/gw3_top/u_gwmc_top/gwmc_bank_ctrl/</td>
<td>0.376</td>
<td>0.376(0.000)</td>
<tr>
<td>top/ddr3_syn_top_inst/u_ddr3/gw3_top/u_gwmc_top/gwmc_rank_ctrl/</td>
<td>0.088</td>
<td>0.088(0.000)</td>
<tr>
<td>top/ddr3_syn_top_inst/u_ddr3/gw3_top/u_gwmc_top/gwmc_timing_ctrl/</td>
<td>0.034</td>
<td>0.034(0.000)</td>
<tr>
<td>top/key_blink_inst/</td>
<td>0.082</td>
<td>0.082(0.000)</td>
</table>
<h2><a name="By_Clock_Domain">Power By Clock Domain:</a></h2>
<table class="detail_table">
<tr>
<th class="label">Clock Domain</th>
<th class="label">Clock Frequency(Mhz)</th>
<th class="label">Total Dynamic Power(mW)</th>
</tr>
<tr>
<td>clk_x1</td>
<td>100.000</td>
<td>198.909</td>
</tr>
<tr>
<td>clk</td>
<td>27.000</td>
<td>45.914</td>
</tr>
<tr>
<td>NO CLOCK DOMAIN</td>
<td>0.000</td>
<td>0.000</td>
</tr>
<tr>
<td>clk_x4</td>
<td>400.000</td>
<td>323.514</td>
</tr>
</table>
</div><!-- content -->
</div><!-- main_wrapper -->
</body>
</html>
