<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>synthesis Report</title>
<style type="text/css">
body { font-family: Verdana, Arial, sans-serif; font-size: 12px; }
div#main_wrapper{ width: 100%; }
div#content { margin-left: 350px; margin-right: 30px; }
div#catalog_wrapper {position: fixed; top: 30px; width: 350px; float: left; }
div#catalog ul { list-style-type: none; }
div#catalog li { text-align: left; list-style-type:circle; color: #0084ff; margin-top: 3px; margin-bottom: 3px; }
div#catalog a { display:inline-block; text-decoration: none; color: #0084ff; font-weight: bold; padding: 3px; }
div#catalog a:visited { color: #0084ff; }
div#catalog a:hover { color: #fff; background: #0084ff; }
hr { margin-top: 30px; margin-bottom: 30px; }
h1, h3 { text-align: center; }
h1 {margin-top: 50px; }
table, th, td { border: 1px solid #aaa; }
table { border-collapse:collapse; margin-top: 10px; margin-bottom: 20px; width: 100%; }
th, td { padding: 5px 5px 5px 5px; }
th { color: #fff; font-weight: bold; background-color: #0084ff; }
table.summary_table td.label { width: 24%; min-width: 200px; background-color: #dee8f4; }
table.detail_table td.label { min-width: 100px; width: 8%;}
</style>
</head>
<body>
<div id="main_wrapper">
<div id="catalog_wrapper">
<div id="catalog">
<ul>
<li><a href="#about" style=" font-size: 16px;">Synthesis Messages</a></li>
<li><a href="#summary" style=" font-size: 16px;">Synthesis Details</a></li>
<li><a href="#resource" style=" font-size: 16px;">Resource</a>
<ul>
<li><a href="#usage" style=" font-size: 14px;">Resource Usage Summary</a></li>
<li><a href="#utilization" style=" font-size: 14px;">Resource Utilization Summary</a></li>
</ul>
</li>
<li><a href="#timing" style=" font-size: 16px;">Timing</a>
<ul>
<li><a href="#clock" style=" font-size: 14px;">Clock Summary</a></li>
<li><a href="#performance" style=" font-size: 14px;">Max Frequency Summary</a></li>
<li><a href="#detail timing" style=" font-size: 14px;">Detail Timing Paths Informations</a></li>
</ul>
</li>
</ul>
</div><!-- catalog -->
</div><!-- catalog_wrapper -->
<div id="content">
<h1><a name="about">Synthesis Messages</a></h1>
<table class="summary_table">
<tr>
<td class="label">Report Title</td>
<td>GowinSynthesis Report</td>
</tr>
<tr>
<td class="label">Design File</td>
<td>G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\ddr3_memory_interface\ddr3_memory_interface.v<br>
G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\ddr3_syn_top.v<br>
G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\gowin_rpll\gowin_rpll.v<br>
G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\key_blink.v<br>
G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\tester.v<br>
G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\top.v<br>
G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\uart_tx_V2.v<br>
</td>
</tr>
<tr>
<td class="label">GowinSynthesis Constraints File</td>
<td>---</td>
</tr>
<tr>
<td class="label">Tool Version</td>
<td>V1.9.11.03 Education</td>
</tr>
<tr>
<td class="label">Part Number</td>
<td>GW2A-LV18PG256C8/I7</td>
</tr>
<tr>
<td class="label">Device</td>
<td>GW2A-18</td>
</tr>
<tr>
<td class="label">Device Version</td>
<td>C</td>
</tr>
<tr>
<td class="label">Created Time</td>
<td>Wed Jul 30 11:45:36 2025
</td>
</tr>
<tr>
<td class="label">Legal Announcement</td>
<td>Copyright (C)2014-2025 Gowin Semiconductor Corporation. ALL rights reserved.</td>
</tr>
</table>
<h1><a name="summary">Synthesis Details</a></h1>
<table class="summary_table">
<tr>
<td class="label">Top Level Module</td>
<td>top</td>
</tr>
<tr>
<td class="label">Synthesis Process</td>
<td>Running parser:<br/>&nbsp;&nbsp;&nbsp;&nbsp;CPU time = 0h 0m 0.484s, Elapsed time = 0h 0m 0.519s, Peak memory usage = 693.008MB<br/>Running netlist conversion:<br/>&nbsp;&nbsp;&nbsp;&nbsp;CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0s, Peak memory usage = 0MB<br/>Running device independent optimization:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Optimizing Phase 0: CPU time = 0h 0m 0.234s, Elapsed time = 0h 0m 0.257s, Peak memory usage = 693.008MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Optimizing Phase 1: CPU time = 0h 0m 0.109s, Elapsed time = 0h 0m 0.109s, Peak memory usage = 693.008MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Optimizing Phase 2: CPU time = 0h 0m 0.203s, Elapsed time = 0h 0m 0.199s, Peak memory usage = 693.008MB<br/>Running inference:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 0: CPU time = 0h 0m 0.078s, Elapsed time = 0h 0m 0.083s, Peak memory usage = 693.008MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 1: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0.005s, Peak memory usage = 693.008MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 2: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0.012s, Peak memory usage = 693.008MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 3: CPU time = 0h 0m 0.015s, Elapsed time = 0h 0m 0.009s, Peak memory usage = 693.008MB<br/>Running technical mapping:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 0: CPU time = 0h 0m 0.171s, Elapsed time = 0h 0m 0.194s, Peak memory usage = 693.008MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 1: CPU time = 0h 0m 0.031s, Elapsed time = 0h 0m 0.028s, Peak memory usage = 693.008MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 2: CPU time = 0h 0m 0.015s, Elapsed time = 0h 0m 0.014s, Peak memory usage = 693.008MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 3: CPU time = 0h 0m 2s, Elapsed time = 0h 0m 3s, Peak memory usage = 693.008MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 4: CPU time = 0h 0m 0.125s, Elapsed time = 0h 0m 0.17s, Peak memory usage = 693.008MB<br/>Generate output files:<br/>&nbsp;&nbsp;&nbsp;&nbsp;CPU time = 0h 0m 0.187s, Elapsed time = 0h 0m 0.187s, Peak memory usage = 693.008MB<br/></td>
</tr>
<tr>
<td class="label">Total Time and Memory Usage</td>
<td>CPU time = 0h 0m 3s, Elapsed time = 0h 0m 4s, Peak memory usage = 693.008MB</td>
</tr>
</table>
<h1><a name="resource">Resource</a></h1>
<h2><a name="usage">Resource Usage Summary</a></h2>
<table class="summary_table">
<tr>
<td class="label"><b>Resource</b></td>
<td><b>Usage</b></td>
</tr>
<tr>
<td class="label"><b>I/O Port </b></td>
<td>165</td>
</tr>
<tr>
<td class="label"><b>I/O Buf </b></td>
<td>162</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspIBUF</td>
<td>7</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspOBUF</td>
<td>134</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspTBUF</td>
<td>2</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspIOBUF</td>
<td>16</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspELVDS_OBUF</td>
<td>1</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspELVDS_IOBUF</td>
<td>2</td>
</tr>
<tr>
<td class="label"><b>Register </b></td>
<td>2046</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFF</td>
<td>336</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFE</td>
<td>265</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFS</td>
<td>1</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFSE</td>
<td>1</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFR</td>
<td>44</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFRE</td>
<td>58</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFP</td>
<td>51</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFPE</td>
<td>3</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFC</td>
<td>905</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFCE</td>
<td>382</td>
</tr>
<tr>
<td class="label"><b>LUT </b></td>
<td>2561</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspLUT2</td>
<td>433</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspLUT3</td>
<td>720</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspLUT4</td>
<td>1408</td>
</tr>
<tr>
<td class="label"><b>ALU </b></td>
<td>304</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspALU</td>
<td>304</td>
</tr>
<tr>
<td class="label"><b>SSRAM </b></td>
<td>110</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspRAM16S4</td>
<td>40</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspRAM16SDP4</td>
<td>70</td>
</tr>
<tr>
<td class="label"><b>INV </b></td>
<td>32</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspINV</td>
<td>32</td>
</tr>
<tr>
<td class="label"><b>IOLOGIC </b></td>
<td>98</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspIDES8_MEM</td>
<td>16</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspOSER8</td>
<td>23</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspOSER8_MEM</td>
<td>20</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspIODELAY</td>
<td>39</td>
</tr>
<tr>
<td class="label"><b>BSRAM </b></td>
<td>13</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspSDPB</td>
<td>5</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspSDPX9B</td>
<td>8</td>
</tr>
<tr>
<td class="label"><b>CLOCK </b></td>
<td>5</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspCLKDIV</td>
<td>1</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDQS</td>
<td>2</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDHCEN</td>
<td>1</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbsprPLL</td>
<td>1</td>
</tr>
</table>
<h2><a name="utilization">Resource Utilization Summary</a></h2>
<table class="summary_table">
<tr>
<td class="label"><b>Resource</b></td>
<td><b>Usage</b></td>
<td><b>Utilization</b></td>
</tr>
<tr>
<td class="label">Logic</td>
<td>3557(2593 LUT, 304 ALU, 110 RAM16) / 20736</td>
<td>18%</td>
</tr>
<tr>
<td class="label">Register</td>
<td>2046 / 16173</td>
<td>13%</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp--Register as Latch</td>
<td>0 / 16173</td>
<td>0%</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp--Register as FF</td>
<td>2046 / 16173</td>
<td>13%</td>
</tr>
<tr>
<td class="label">BSRAM</td>
<td>13 / 46</td>
<td>29%</td>
</tr>
</table>
<h1><a name="timing">Timing</a></h1>
<h2><a name="clock">Clock Summary:</a></h2>
<table class="summary_table">
<tr>
<th>NO.</th>
<th>Clock Name</th>
<th>Type</th>
<th>Period</th>
<th>Frequency(MHz)</th>
<th>Rise</th>
<th>Fall</th>
<th>Source</th>
<th>Master</th>
<th>Object</th>
</tr>
<tr>
<td>1</td>
<td>clk</td>
<td>Base</td>
<td>37.037</td>
<td>27.000</td>
<td>0.000</td>
<td>18.519</td>
<td> </td>
<td> </td>
<td>clk_ibuf/I </td>
</tr>
<tr>
<td>2</td>
<td>ddr3_syn_top_inst/pll/rpll_inst/CLKOUT.default_gen_clk</td>
<td>Generated</td>
<td>2.511</td>
<td>398.250</td>
<td>0.000</td>
<td>1.255</td>
<td>clk_ibuf/I</td>
<td>clk</td>
<td>ddr3_syn_top_inst/pll/rpll_inst/CLKOUT </td>
</tr>
<tr>
<td>3</td>
<td>ddr3_syn_top_inst/pll/rpll_inst/CLKOUTP.default_gen_clk</td>
<td>Generated</td>
<td>2.511</td>
<td>398.250</td>
<td>0.000</td>
<td>1.255</td>
<td>clk_ibuf/I</td>
<td>clk</td>
<td>ddr3_syn_top_inst/pll/rpll_inst/CLKOUTP </td>
</tr>
<tr>
<td>4</td>
<td>ddr3_syn_top_inst/pll/rpll_inst/CLKOUTD.default_gen_clk</td>
<td>Generated</td>
<td>5.022</td>
<td>199.125</td>
<td>0.000</td>
<td>2.511</td>
<td>clk_ibuf/I</td>
<td>clk</td>
<td>ddr3_syn_top_inst/pll/rpll_inst/CLKOUTD </td>
</tr>
<tr>
<td>5</td>
<td>ddr3_syn_top_inst/pll/rpll_inst/CLKOUTD3.default_gen_clk</td>
<td>Generated</td>
<td>7.533</td>
<td>132.750</td>
<td>0.000</td>
<td>3.766</td>
<td>clk_ibuf/I</td>
<td>clk</td>
<td>ddr3_syn_top_inst/pll/rpll_inst/CLKOUTD3 </td>
</tr>
<tr>
<td>6</td>
<td>ddr3_syn_top_inst/u_ddr3/gw3_top/i4/fclkdiv/CLKOUT.default_gen_clk</td>
<td>Generated</td>
<td>10.044</td>
<td>99.562</td>
<td>0.000</td>
<td>5.022</td>
<td>ddr3_syn_top_inst/pll/rpll_inst/CLKOUT</td>
<td>ddr3_syn_top_inst/pll/rpll_inst/CLKOUT.default_gen_clk</td>
<td>ddr3_syn_top_inst/u_ddr3/gw3_top/i4/fclkdiv/CLKOUT </td>
</tr>
</table>
<h2><a name="performance">Max Frequency Summary:</a></h2>
<table class="summary_table">
<tr>
<th>NO.</th>
<th>Clock Name</th>
<th>Constraint</th>
<th>Actual Fmax</th>
<th>Logic Level</th>
<th>Entity</th>
</tr>
<tr>
<td>1</td>
<td>clk</td>
<td>27.000(MHz)</td>
<td>118.161(MHz)</td>
<td>9</td>
<td>TOP</td>
</tr>
<tr>
<td>2</td>
<td>ddr3_syn_top_inst/pll/rpll_inst/CLKOUT.default_gen_clk</td>
<td>398.250(MHz)</td>
<td>1030.928(MHz)</td>
<td>1</td>
<td>TOP</td>
</tr>
<tr>
<td>3</td>
<td>ddr3_syn_top_inst/u_ddr3/gw3_top/i4/fclkdiv/CLKOUT.default_gen_clk</td>
<td>99.562(MHz)</td>
<td>106.856(MHz)</td>
<td>19</td>
<td>TOP</td>
</tr>
</table>
<h2><a name="detail timing">Detail Timing Paths Information</a></h2>
<h3>Path&nbsp1</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>-4.363</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>115.764</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>111.401</td>
</tr>
<tr>
<td class="label">From</td>
<td>ddr3_syn_top_inst/test/error_bit_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>ddr3_syn_top_inst/test/print_buffer_970_s6</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>ddr3_syn_top_inst/u_ddr3/gw3_top/i4/fclkdiv/CLKOUT.default_gen_clk[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>110.483</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>ddr3_syn_top_inst/u_ddr3/gw3_top/i4/fclkdiv/CLKOUT.default_gen_clk</td>
</tr>
<tr>
<td>110.824</td>
<td>0.341</td>
<td>tCL</td>
<td>RR</td>
<td>1887</td>
<td>ddr3_syn_top_inst/u_ddr3/gw3_top/i4/fclkdiv/CLKOUT</td>
</tr>
<tr>
<td>111.184</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/error_bit_s0/CLK</td>
</tr>
<tr>
<td>111.416</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>2</td>
<td>ddr3_syn_top_inst/test/error_bit_s0/Q</td>
</tr>
<tr>
<td>111.890</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/n23136_s3/I2</td>
</tr>
<tr>
<td>112.343</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>10</td>
<td>ddr3_syn_top_inst/test/n23136_s3/F</td>
</tr>
<tr>
<td>112.817</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_1004_s14/I0</td>
</tr>
<tr>
<td>113.334</td>
<td>0.517</td>
<td>tINS</td>
<td>FF</td>
<td>5</td>
<td>ddr3_syn_top_inst/test/print_buffer_1004_s14/F</td>
</tr>
<tr>
<td>113.808</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_994_s11/I1</td>
</tr>
<tr>
<td>114.363</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>ddr3_syn_top_inst/test/print_buffer_994_s11/F</td>
</tr>
<tr>
<td>114.837</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_970_s12/I2</td>
</tr>
<tr>
<td>115.290</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_970_s12/F</td>
</tr>
<tr>
<td>115.764</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_970_s6/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>111.111</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>111.111</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>111.111</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>357</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>111.471</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_970_s6/CLK</td>
</tr>
<tr>
<td>111.436</td>
<td>-0.035</td>
<td>tUnc</td>
<td> </td>
<td> </td>
<td>ddr3_syn_top_inst/test/print_buffer_970_s6</td>
</tr>
<tr>
<td>111.401</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_970_s6</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>-0.341</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>0.628</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>5</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 1.978, 43.188%; route: 2.370, 51.746%; tC2Q: 0.232, 5.066%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
<h3>Path&nbsp2</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>-4.363</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>115.764</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>111.401</td>
</tr>
<tr>
<td class="label">From</td>
<td>ddr3_syn_top_inst/test/error_bit_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>ddr3_syn_top_inst/test/print_buffer_922_s12</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>ddr3_syn_top_inst/u_ddr3/gw3_top/i4/fclkdiv/CLKOUT.default_gen_clk[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>110.483</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>ddr3_syn_top_inst/u_ddr3/gw3_top/i4/fclkdiv/CLKOUT.default_gen_clk</td>
</tr>
<tr>
<td>110.824</td>
<td>0.341</td>
<td>tCL</td>
<td>RR</td>
<td>1887</td>
<td>ddr3_syn_top_inst/u_ddr3/gw3_top/i4/fclkdiv/CLKOUT</td>
</tr>
<tr>
<td>111.184</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/error_bit_s0/CLK</td>
</tr>
<tr>
<td>111.416</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>2</td>
<td>ddr3_syn_top_inst/test/error_bit_s0/Q</td>
</tr>
<tr>
<td>111.890</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/n23136_s3/I2</td>
</tr>
<tr>
<td>112.343</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>10</td>
<td>ddr3_syn_top_inst/test/n23136_s3/F</td>
</tr>
<tr>
<td>112.817</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/n23174_s3/I0</td>
</tr>
<tr>
<td>113.334</td>
<td>0.517</td>
<td>tINS</td>
<td>FF</td>
<td>5</td>
<td>ddr3_syn_top_inst/test/n23174_s3/F</td>
</tr>
<tr>
<td>113.808</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/n23112_s4/I2</td>
</tr>
<tr>
<td>114.261</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>3</td>
<td>ddr3_syn_top_inst/test/n23112_s4/F</td>
</tr>
<tr>
<td>114.735</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_922_s14/I1</td>
</tr>
<tr>
<td>115.290</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_922_s14/F</td>
</tr>
<tr>
<td>115.764</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_922_s12/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>111.111</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>111.111</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>111.111</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>357</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>111.471</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_922_s12/CLK</td>
</tr>
<tr>
<td>111.436</td>
<td>-0.035</td>
<td>tUnc</td>
<td> </td>
<td> </td>
<td>ddr3_syn_top_inst/test/print_buffer_922_s12</td>
</tr>
<tr>
<td>111.401</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_922_s12</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>-0.341</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>0.628</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>5</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 1.978, 43.188%; route: 2.370, 51.746%; tC2Q: 0.232, 5.066%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
<h3>Path&nbsp3</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>-4.363</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>115.764</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>111.401</td>
</tr>
<tr>
<td class="label">From</td>
<td>ddr3_syn_top_inst/test/error_bit_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>ddr3_syn_top_inst/test/print_buffer_837_s12</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>ddr3_syn_top_inst/u_ddr3/gw3_top/i4/fclkdiv/CLKOUT.default_gen_clk[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>110.483</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>ddr3_syn_top_inst/u_ddr3/gw3_top/i4/fclkdiv/CLKOUT.default_gen_clk</td>
</tr>
<tr>
<td>110.824</td>
<td>0.341</td>
<td>tCL</td>
<td>RR</td>
<td>1887</td>
<td>ddr3_syn_top_inst/u_ddr3/gw3_top/i4/fclkdiv/CLKOUT</td>
</tr>
<tr>
<td>111.184</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/error_bit_s0/CLK</td>
</tr>
<tr>
<td>111.416</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>2</td>
<td>ddr3_syn_top_inst/test/error_bit_s0/Q</td>
</tr>
<tr>
<td>111.890</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/n23136_s3/I2</td>
</tr>
<tr>
<td>112.343</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>10</td>
<td>ddr3_syn_top_inst/test/n23136_s3/F</td>
</tr>
<tr>
<td>112.817</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/n23174_s3/I0</td>
</tr>
<tr>
<td>113.334</td>
<td>0.517</td>
<td>tINS</td>
<td>FF</td>
<td>5</td>
<td>ddr3_syn_top_inst/test/n23174_s3/F</td>
</tr>
<tr>
<td>113.808</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/n23112_s4/I2</td>
</tr>
<tr>
<td>114.261</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>3</td>
<td>ddr3_syn_top_inst/test/n23112_s4/F</td>
</tr>
<tr>
<td>114.735</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_858_s13/I1</td>
</tr>
<tr>
<td>115.290</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_858_s13/F</td>
</tr>
<tr>
<td>115.764</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_837_s12/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>111.111</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>111.111</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>111.111</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>357</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>111.471</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_837_s12/CLK</td>
</tr>
<tr>
<td>111.436</td>
<td>-0.035</td>
<td>tUnc</td>
<td> </td>
<td> </td>
<td>ddr3_syn_top_inst/test/print_buffer_837_s12</td>
</tr>
<tr>
<td>111.401</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_837_s12</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>-0.341</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>0.628</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>5</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 1.978, 43.188%; route: 2.370, 51.746%; tC2Q: 0.232, 5.066%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
<h3>Path&nbsp4</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>-4.363</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>115.764</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>111.401</td>
</tr>
<tr>
<td class="label">From</td>
<td>ddr3_syn_top_inst/test/error_bit_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>ddr3_syn_top_inst/test/print_buffer_818_s12</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>ddr3_syn_top_inst/u_ddr3/gw3_top/i4/fclkdiv/CLKOUT.default_gen_clk[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>110.483</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>ddr3_syn_top_inst/u_ddr3/gw3_top/i4/fclkdiv/CLKOUT.default_gen_clk</td>
</tr>
<tr>
<td>110.824</td>
<td>0.341</td>
<td>tCL</td>
<td>RR</td>
<td>1887</td>
<td>ddr3_syn_top_inst/u_ddr3/gw3_top/i4/fclkdiv/CLKOUT</td>
</tr>
<tr>
<td>111.184</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/error_bit_s0/CLK</td>
</tr>
<tr>
<td>111.416</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>2</td>
<td>ddr3_syn_top_inst/test/error_bit_s0/Q</td>
</tr>
<tr>
<td>111.890</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/n23136_s3/I2</td>
</tr>
<tr>
<td>112.343</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>10</td>
<td>ddr3_syn_top_inst/test/n23136_s3/F</td>
</tr>
<tr>
<td>112.817</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/n23100_s3/I0</td>
</tr>
<tr>
<td>113.334</td>
<td>0.517</td>
<td>tINS</td>
<td>FF</td>
<td>3</td>
<td>ddr3_syn_top_inst/test/n23100_s3/F</td>
</tr>
<tr>
<td>113.808</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/n23257_s4/I2</td>
</tr>
<tr>
<td>114.261</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>ddr3_syn_top_inst/test/n23257_s4/F</td>
</tr>
<tr>
<td>114.735</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_818_s14/I1</td>
</tr>
<tr>
<td>115.290</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_818_s14/F</td>
</tr>
<tr>
<td>115.764</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_818_s12/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>111.111</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>111.111</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>111.111</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>357</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>111.471</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_818_s12/CLK</td>
</tr>
<tr>
<td>111.436</td>
<td>-0.035</td>
<td>tUnc</td>
<td> </td>
<td> </td>
<td>ddr3_syn_top_inst/test/print_buffer_818_s12</td>
</tr>
<tr>
<td>111.401</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_818_s12</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>-0.341</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>0.628</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>5</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 1.978, 43.188%; route: 2.370, 51.746%; tC2Q: 0.232, 5.066%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
<h3>Path&nbsp5</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>-4.281</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>115.682</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>111.401</td>
</tr>
<tr>
<td class="label">From</td>
<td>ddr3_syn_top_inst/test/error_bit_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>ddr3_syn_top_inst/test/print_buffer_806_s6</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>ddr3_syn_top_inst/u_ddr3/gw3_top/i4/fclkdiv/CLKOUT.default_gen_clk[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>110.483</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>ddr3_syn_top_inst/u_ddr3/gw3_top/i4/fclkdiv/CLKOUT.default_gen_clk</td>
</tr>
<tr>
<td>110.824</td>
<td>0.341</td>
<td>tCL</td>
<td>RR</td>
<td>1887</td>
<td>ddr3_syn_top_inst/u_ddr3/gw3_top/i4/fclkdiv/CLKOUT</td>
</tr>
<tr>
<td>111.184</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/error_bit_s0/CLK</td>
</tr>
<tr>
<td>111.416</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>2</td>
<td>ddr3_syn_top_inst/test/error_bit_s0/Q</td>
</tr>
<tr>
<td>111.890</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/n23136_s3/I2</td>
</tr>
<tr>
<td>112.343</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>10</td>
<td>ddr3_syn_top_inst/test/n23136_s3/F</td>
</tr>
<tr>
<td>112.817</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/n23097_s4/I0</td>
</tr>
<tr>
<td>113.334</td>
<td>0.517</td>
<td>tINS</td>
<td>FF</td>
<td>5</td>
<td>ddr3_syn_top_inst/test/n23097_s4/F</td>
</tr>
<tr>
<td>113.808</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_987_s15/I3</td>
</tr>
<tr>
<td>114.179</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_987_s15/F</td>
</tr>
<tr>
<td>114.653</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_806_s12/I1</td>
</tr>
<tr>
<td>115.208</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_806_s12/F</td>
</tr>
<tr>
<td>115.682</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_806_s6/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>111.111</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>111.111</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>111.111</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>357</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>111.471</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_806_s6/CLK</td>
</tr>
<tr>
<td>111.436</td>
<td>-0.035</td>
<td>tUnc</td>
<td> </td>
<td> </td>
<td>ddr3_syn_top_inst/test/print_buffer_806_s6</td>
</tr>
<tr>
<td>111.401</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>ddr3_syn_top_inst/test/print_buffer_806_s6</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>-0.341</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>0.628</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>5</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 1.896, 42.152%; route: 2.370, 52.690%; tC2Q: 0.232, 5.158%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
</div><!-- content -->
</div><!-- main_wrapper -->
</body>
</html>
