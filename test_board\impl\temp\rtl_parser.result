[{"InstFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/top.v", "InstLine": 1, "InstName": "top", "ModuleFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/top.v", "ModuleLine": 1, "ModuleName": "top", "SubInsts": [{"InstFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/top.v", "InstLine": 59, "InstName": "key_blink_inst", "ModuleFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/key_blink.v", "ModuleLine": 1, "ModuleName": "key_blink"}, {"InstFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/top.v", "InstLine": 85, "InstName": "ddr3_syn_top_inst", "ModuleFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/ddr3_syn_top.v", "ModuleLine": 3, "ModuleName": "ddr3_syn_top", "SubInsts": [{"InstFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/ddr3_syn_top.v", "InstLine": 75, "InstName": "test", "ModuleFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/tester.v", "ModuleLine": 1, "ModuleName": "tester", "SubInsts": [{"InstFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/print.v", "InstLine": 105, "InstName": "tx", "ModuleFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/uart_tx_V2.v", "ModuleLine": 1, "ModuleName": "uart_tx_V2"}]}, {"InstFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/ddr3_syn_top.v", "InstLine": 103, "InstName": "pll", "ModuleFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/gowin_rpll/gowin_rpll.v", "ModuleLine": 9, "ModuleName": "Gowin_rPLL"}, {"InstFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/ddr3_syn_top.v", "InstLine": 113, "InstName": "u_ddr3", "ModuleFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/ddr3_memory_interface/ddr3_memory_interface.v", "ModuleLine": 11484, "ModuleName": "DDR3_Memory_Interface_Top", "SubInsts": [{"InstFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/ddr3_memory_interface/ddr3_memory_interface.v", "InstLine": 11569, "InstName": "gw3_top", "ModuleFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/ddr3_memory_interface/ddr3_memory_interface.v", "ModuleLine": 10, "ModuleName": "~GW_DDR3_PHY_MC.DDR3_Memory_Interface_Top"}]}]}]}]