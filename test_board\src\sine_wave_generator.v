module sine_wave_generator #(
    parameter FREQ_DIV = 1000,      // 分频系数，控制正弦波频率
    parameter PHASE_WIDTH = 8,      // 相位累加器位宽 (简化为8位)
    parameter PWM_WIDTH = 8         // PWM计数器位宽
)(
    input wire clk,
    input wire rst_n,
    output reg sine_out,
    output wire [7:0] sine_amplitude  // 用于调试的幅度输出
);

    // 分频计数器
    reg [$clog2(FREQ_DIV)-1:0] freq_counter;

    // 相位累加器
    reg [PHASE_WIDTH-1:0] phase_acc;

    // PWM计数器
    reg [PWM_WIDTH-1:0] pwm_counter;

    // 简化的正弦查找表 (64点，8位精度)
    // 存储一个完整周期的正弦值
    reg [7:0] sine_lut [0:63];
    
    // 初始化64点正弦查找表 (一个完整周期，范围0-255)
    initial begin
        sine_lut[0]  = 128; sine_lut[1]  = 140; sine_lut[2]  = 152; sine_lut[3]  = 164;
        sine_lut[4]  = 176; sine_lut[5]  = 188; sine_lut[6]  = 199; sine_lut[7]  = 210;
        sine_lut[8]  = 221; sine_lut[9]  = 231; sine_lut[10] = 240; sine_lut[11] = 249;
        sine_lut[12] = 255; sine_lut[13] = 255; sine_lut[14] = 255; sine_lut[15] = 255;
        sine_lut[16] = 249; sine_lut[17] = 240; sine_lut[18] = 231; sine_lut[19] = 221;
        sine_lut[20] = 210; sine_lut[21] = 199; sine_lut[22] = 188; sine_lut[23] = 176;
        sine_lut[24] = 164; sine_lut[25] = 152; sine_lut[26] = 140; sine_lut[27] = 128;
        sine_lut[28] = 115; sine_lut[29] = 103; sine_lut[30] = 91;  sine_lut[31] = 79;
        sine_lut[32] = 67;  sine_lut[33] = 56;  sine_lut[34] = 45;  sine_lut[35] = 34;
        sine_lut[36] = 24;  sine_lut[37] = 15;  sine_lut[38] = 6;   sine_lut[39] = 0;
        sine_lut[40] = 0;   sine_lut[41] = 0;   sine_lut[42] = 0;   sine_lut[43] = 6;
        sine_lut[44] = 15;  sine_lut[45] = 24;  sine_lut[46] = 34;  sine_lut[47] = 45;
        sine_lut[48] = 56;  sine_lut[49] = 67;  sine_lut[50] = 79;  sine_lut[51] = 91;
        sine_lut[52] = 103; sine_lut[53] = 115; sine_lut[54] = 128; sine_lut[55] = 140;
        sine_lut[56] = 152; sine_lut[57] = 164; sine_lut[58] = 176; sine_lut[59] = 188;
        sine_lut[60] = 199; sine_lut[61] = 210; sine_lut[62] = 221; sine_lut[63] = 231;
    end

    // 当前正弦值
    reg [7:0] current_sine_value;

    // 分频和相位累加
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            freq_counter <= 0;
            phase_acc <= 0;
            current_sine_value <= 128;
        end else begin
            if (freq_counter >= FREQ_DIV - 1) begin
                freq_counter <= 0;
                phase_acc <= phase_acc + 1;
                current_sine_value <= sine_lut[phase_acc[5:0]]; // 使用低6位作为索引
            end else begin
                freq_counter <= freq_counter + 1;
            end
        end
    end

    // PWM输出生成
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            pwm_counter <= 0;
            sine_out <= 1'b0;
        end else begin
            pwm_counter <= pwm_counter + 1;
            sine_out <= (pwm_counter < current_sine_value) ? 1'b1 : 1'b0;
        end
    end

    assign sine_amplitude = current_sine_value;

endmodule
