{"Device": "GW2A-18C", "Files": [{"Path": "G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/ddr3_memory_interface/ddr3_memory_interface.v", "Type": "verilog"}, {"Path": "G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/ddr3_syn_top.v", "Type": "verilog"}, {"Path": "G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/gowin_rpll/gowin_rpll.v", "Type": "verilog"}, {"Path": "G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/key_blink.v", "Type": "verilog"}, {"Path": "G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/tester.v", "Type": "verilog"}, {"Path": "G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/top.v", "Type": "verilog"}, {"Path": "G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/uart_tx_V2.v", "Type": "verilog"}], "IncludePath": [], "LoopLimit": 2000, "ResultFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/impl/temp/rtl_parser.result", "Top": "", "VerilogStd": "verilog_2001", "VhdlStd": "vhdl_93"}